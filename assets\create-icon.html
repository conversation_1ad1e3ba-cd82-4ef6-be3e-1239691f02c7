<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة التطبيق</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .icon-preview { width: 256px; height: 256px; margin: 20px auto; border: 2px solid #ddd; border-radius: 20px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 4rem; }
        .download-btn { background: #667eea; color: white; padding: 15px 30px; border: none; border-radius: 10px; font-size: 1.1rem; cursor: pointer; margin: 20px; }
    </style>
</head>
<body>
    <h1>أيقونة تطبيق مصطفي كشاف للمفروشات</h1>
    <div class="icon-preview" id="iconPreview">
        🏠
    </div>
    <button class="download-btn" onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        function downloadIcon() {
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');
            
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 256, 256);
            
            // رسم الأيقونة
            ctx.font = '120px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🏠', 128, 128);
            
            // تحميل الصورة
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
