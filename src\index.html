<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مبيعات عمرو السيد - نظام المبيعات</title>
    <!-- Material Design Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />

    <!-- Font Awesome Icons - احتياطي للتوافق -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- خط Beiruti العربي الجميل -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Beiruti:wght@200..900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-couch logo-icon"></i>
                    <h1>نظام مبيعات عمرو السيد</h1>
                    <p>نظام إدارة المبيعات</p>
                </div>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <div class="input-group">
                        <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" id="password" placeholder="أدخل كلمة المرور">
                    </div>
                </div>
                
                <button type="button" class="login-btn" onclick="login()">
                    تسجيل الدخول
                </button>
                
                <div class="login-options">
                    <a href="#" onclick="showRegisterForm()">إنشاء حساب جديد</a>
                    <p>تم التطوير بوسطة عمرو السيد اليماني</p>
                </div>

            </div>
            
            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <!-- شاشة التسجيل -->
    <div id="registerScreen" class="login-screen" style="display: none;">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-couch logo-icon"></i>
                    <h1>إنشاء حساب جديد</h1>
                    <p>نظام مبيعات عمرو السيد</p>
                </div>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <div class="input-group">
                        <input type="text" id="newUsername" placeholder="أدخل اسم المستخدم">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <input type="text" id="fullName" placeholder="أدخل الاسم الكامل">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" id="newPassword" placeholder="أدخل كلمة المرور">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                    </div>
                </div>
                
                <button type="button" class="login-btn" onclick="register()">
                    إنشاء الحساب
                </button>
                
                <div class="login-options">
                    <a href="#" onclick="showLoginForm()">العودة لتسجيل الدخول</a>
                </div>
            </div>
            
            <div class="error-message" id="registerErrorMessage"></div>
        </div>
    </div>

    <!-- الواجهة الرئيسية -->
    <div id="mainApp" class="main-app" style="display: none;">
        <!-- شريط التنقل العلوي -->
        <nav class="navbar">
            <div class="nav-brand">
                <span class="material-symbols-outlined">chair</span>
                <span>نظام مبيعات عمرو السيد</span>
            </div>

            <!-- مربع البحث الشامل -->
            <div class="global-search">
                <div class="search-container">
                    <input type="text" id="globalSearchInput" placeholder="ابحث في المنتجات، العملاء، أو الفواتير..." autocomplete="off">
                    <span class="material-symbols-outlined search-icon">search</span>
                </div>
                <div id="searchResults" class="search-results"></div>
            </div>

            <div class="header-right">
                <div class="notifications">
                    <span class="material-symbols-outlined notifications-icon">
                        notifications
                    </span>
                    <span class="notification-badge" id="notificationBadge">0</span>
                    <div class="notifications-menu" id="notificationsMenu">
                        <div class="notifications-header">
                            <h3>الإشعارات</h3>
                            <button class="clear-all-btn" id="clearAllNotificationsBtn">
                                <span class="material-symbols-outlined">done_all</span>
                                مسح الكل
                            </button>
                        </div>
                        <div class="notifications-list" id="notificationsList">
                            <!-- Notifications will be rendered here -->
                        </div>
                        <div class="no-notifications" id="noNotifications">
                            <span class="material-symbols-outlined">notifications_off</span>
                            <p>لا توجد إشعارات حالياً.</p>
                        </div>
                    </div>
                </div>
                <div class="user-profile">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <span class="material-symbols-outlined">account_circle</span>
                        <span id="currentUser">مرحباً، </span>
                        <span class="material-symbols-outlined dropdown-icon">arrow_drop_down</span>
                    </div>
                    <div class="user-menu" id="userMenu">
                        <div class="menu-item" onclick="refreshApp()">
                            <span class="material-symbols-outlined">refresh</span>
                            تحديث
                        </div>
                        <div class="menu-item" onclick="showSupport()">
                            <span class="material-symbols-outlined">support_agent</span>
                            الدعم
                        </div>
                        <div class="menu-item" onclick="showDeveloperInfo()">
                            <span class="material-symbols-outlined">code</span>
                            المطور
                        </div>
                        <div class="menu-divider"></div>
                        <div class="menu-item logout" onclick="logout()">
                            <span class="material-symbols-outlined">logout</span>
                            تسجيل الخروج
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item active" data-page="dashboard">
                    <span class="material-symbols-outlined">dashboard</span>
                    <span>لوحة التحكم</span>
                </li>
                <li class="menu-item" data-page="products">
                    <span class="material-symbols-outlined">inventory_2</span>
                    <span>المنتجات</span>
                </li>
                <li class="menu-item" data-page="customers">
                    <span class="material-symbols-outlined">people</span>
                    <span>العملاء</span>
                </li>
                <li class="menu-item" data-page="invoices">
                    <span class="material-symbols-outlined">receipt_long</span>
                    <span>الفواتير</span>
                </li>
                <li class="menu-item" data-page="reports">
                    <span class="material-symbols-outlined">analytics</span>
                    <span>التقارير</span>
                </li>
                <li class="menu-item" data-page="activity">
                    <span class="material-symbols-outlined">history</span>
                    <span>سجل الأنشطة</span>
                </li>
                <li class="menu-item" data-page="settings">
                    <span class="material-symbols-outlined">settings</span>
                    <span>الإعدادات</span>
                </li>
            </ul>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <div id="pageContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/error-handler.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/activity-log.js"></script>
    <script src="js/backup.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/search.js"></script>
    <script src="js/global-search.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/notifications-dropdown.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/shortcuts.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/products.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/invoice.js"></script>
    <script src="js/print.js"></script>

    <!-- إصلاح مشكلة الأيقونات -->
    <script>
        // التحقق من تحميل Font Awesome وإضافة حل احتياطي
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق الحل البسيط فوراً كاحتياط
            document.body.classList.add('simple-icons');

            // إنشاء عنصر اختبار للتحقق من تحميل الأيقونات
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            testIcon.style.visibility = 'hidden';
            document.body.appendChild(testIcon);

            // التحقق بعد تأخير قصير
            setTimeout(() => {
                try {
                    const computedStyle = window.getComputedStyle(testIcon, ':before');
                    const content = computedStyle.getPropertyValue('content');

                    // إذا تم تحميل الأيقونات بنجاح، إزالة الحل البسيط
                    if (content && content !== 'none' && content !== '""' && !content.includes('الرئيسية')) {
                        document.body.classList.remove('simple-icons');
                        console.log('✅ تم تحميل Font Awesome بنجاح');
                    } else {
                        console.log('⚠️ استخدام الأيقونات البديلة');

                        // محاولة تحميل رابط احتياطي
                        const fallbackLink = document.createElement('link');
                        fallbackLink.rel = 'stylesheet';
                        fallbackLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
                        fallbackLink.crossOrigin = 'anonymous';

                        fallbackLink.onload = () => {
                            // إعادة التحقق بعد تحميل الرابط الاحتياطي
                            setTimeout(() => {
                                try {
                                    const newComputedStyle = window.getComputedStyle(testIcon, ':before');
                                    const newContent = newComputedStyle.getPropertyValue('content');
                                    if (newContent && newContent !== 'none' && newContent !== '""' && !newContent.includes('الرئيسية')) {
                                        document.body.classList.remove('simple-icons');
                                        console.log('✅ تم تحميل Font Awesome من الرابط الاحتياطي');
                                    }
                                } catch (e) {
                                    console.log('⚠️ الاستمرار مع الأيقونات البديلة');
                                }
                            }, 300);
                        };

                        fallbackLink.onerror = () => {
                            console.log('⚠️ فشل تحميل الرابط الاحتياطي - استخدام الأيقونات البديلة');
                        };

                        document.head.appendChild(fallbackLink);
                    }
                } catch (e) {
                    console.log('⚠️ خطأ في التحقق من الأيقونات - استخدام البديل');
                }

                // إزالة عنصر الاختبار
                try {
                    if (testIcon.parentNode) {
                        document.body.removeChild(testIcon);
                    }
                } catch (e) {
                    // تجاهل الخطأ
                }
            }, 800);
        });

        // إضافة CSS احتياطي للأيقونات
        const iconFallbackCSS = `
            /* أيقونات احتياطية بـ Unicode */
            .fas.fa-home:before { content: "🏠"; font-family: inherit !important; }
            .fas.fa-box:before { content: "📦"; font-family: inherit !important; }
            .fas.fa-users:before { content: "👥"; font-family: inherit !important; }
            .fas.fa-file-invoice:before { content: "🧾"; font-family: inherit !important; }
            .fas.fa-chart-bar:before { content: "📊"; font-family: inherit !important; }
            .fas.fa-cog:before { content: "⚙️"; font-family: inherit !important; }
            .fas.fa-sign-out-alt:before { content: "🚪"; font-family: inherit !important; }
            .fas.fa-plus:before { content: "➕"; font-family: inherit !important; }
            .fas.fa-edit:before { content: "✏️"; font-family: inherit !important; }
            .fas.fa-trash:before { content: "🗑️"; font-family: inherit !important; }
            .fas.fa-eye:before { content: "👁️"; font-family: inherit !important; }
            .fas.fa-print:before { content: "🖨️"; font-family: inherit !important; }
            .fas.fa-search:before { content: "🔍"; font-family: inherit !important; }
            .fas.fa-user:before { content: "👤"; font-family: inherit !important; }
            .fas.fa-phone:before { content: "📞"; font-family: inherit !important; }
            .fas.fa-envelope:before { content: "✉️"; font-family: inherit !important; }
            .fas.fa-check-circle:before { content: "✅"; font-family: inherit !important; }
            .fas.fa-exclamation-circle:before { content: "❗"; font-family: inherit !important; }
            .fas.fa-exclamation-triangle:before { content: "⚠️"; font-family: inherit !important; }
            .fas.fa-info-circle:before { content: "ℹ️"; font-family: inherit !important; }
            .fas.fa-times:before { content: "❌"; font-family: inherit !important; }
            .fas.fa-couch:before { content: "🛋️"; font-family: inherit !important; }
            .fas.fa-user-circle:before { content: "👤"; font-family: inherit !important; }
            .fas.fa-spinner:before { content: "⏳"; font-family: inherit !important; }
        `;

        // إضافة CSS الاحتياطي
        const style = document.createElement('style');
        style.textContent = iconFallbackCSS;
        document.head.appendChild(style);
    </script>
</body>
</html>
