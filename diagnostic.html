<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - مصطفي كشاف للمفروشات</title>
    <link rel="stylesheet" href="src/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1><i class="fas fa-stethoscope"></i> تشخيص النظام</h1>
        <p>هذه الصفحة تساعد في تشخيص مشاكل النظام وإصلاحها</p>

        <div class="test-section">
            <h2><i class="fas fa-cog"></i> فحص الملفات الأساسية</h2>
            <button onclick="checkFiles()" class="btn btn-primary">فحص الملفات</button>
            <div id="fileResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-database"></i> فحص نظام التخزين</h2>
            <button onclick="checkStorage()" class="btn btn-primary">فحص التخزين</button>
            <div id="storageResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-window-maximize"></i> فحص النوافذ المنبثقة</h2>
            <button onclick="checkModals()" class="btn btn-primary">فحص النوافذ</button>
            <button onclick="testProductModal()" class="btn btn-secondary">اختبار نافذة المنتج</button>
            <div id="modalResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-exclamation-triangle"></i> سجل الأخطاء</h2>
            <button onclick="showErrors()" class="btn btn-primary">عرض الأخطاء</button>
            <button onclick="clearErrors()" class="btn btn-danger">مسح الأخطاء</button>
            <div id="errorResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-tools"></i> أدوات الإصلاح</h2>
            <button onclick="resetStorage()" class="btn btn-warning">إعادة تعيين التخزين</button>
            <button onclick="reloadPage()" class="btn btn-info">إعادة تحميل الصفحة</button>
            <div id="repairResults"></div>
        </div>

        <!-- نافذة اختبار المنتج -->
        <div id="testProductModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">اختبار إضافة منتج</h2>
                    <button class="close-btn" onclick="closeTestProductModal()">&times;</button>
                </div>
                
                <form id="testProductForm" onsubmit="saveTestProduct(event)">
                    <div style="padding: 20px;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="testProductName">اسم المنتج *</label>
                                <input type="text" id="testProductName" required>
                            </div>
                            <div class="form-group">
                                <label for="testProductCategory">الفئة *</label>
                                <select id="testProductCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="كنب">كنب</option>
                                    <option value="طاولات">طاولات</option>
                                    <option value="كراسي">كراسي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="testProductPrice">سعر البيع *</label>
                                <input type="number" id="testProductPrice" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="testProductStock">الكمية</label>
                                <input type="number" id="testProductStock" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ اختبار
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeTestProductModal()">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="src/js/error-handler.js"></script>
    <script src="src/js/storage.js"></script>
    <script src="src/js/products.js"></script>
    
    <script>
        function checkFiles() {
            const results = document.getElementById('fileResults');
            const files = [
                'src/js/storage.js',
                'src/js/products.js',
                'src/js/app.js',
                'src/css/style.css'
            ];
            
            let html = '<h3>نتائج فحص الملفات:</h3>';
            
            files.forEach(file => {
                // محاولة فحص وجود الملف عبر fetch
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            html += `<div class="test-result test-success">✅ ${file} - موجود</div>`;
                        } else {
                            html += `<div class="test-result test-error">❌ ${file} - غير موجود</div>`;
                        }
                        results.innerHTML = html;
                    })
                    .catch(error => {
                        html += `<div class="test-result test-error">❌ ${file} - خطأ في التحميل</div>`;
                        results.innerHTML = html;
                    });
            });
        }

        function checkStorage() {
            const results = document.getElementById('storageResults');
            let html = '<h3>نتائج فحص التخزين:</h3>';
            
            try {
                // فحص وجود كائن التخزين
                if (typeof storage !== 'undefined') {
                    html += '<div class="test-result test-success">✅ كائن التخزين موجود</div>';
                    
                    // اختبار الكتابة والقراءة
                    const testData = { test: 'diagnostic', time: new Date().toISOString() };
                    const writeResult = storage.writeFile('diagnostic-test.json', testData);
                    
                    if (writeResult) {
                        html += '<div class="test-result test-success">✅ كتابة البيانات تعمل</div>';
                        
                        const readResult = storage.readFile('diagnostic-test.json');
                        if (readResult && readResult.test === 'diagnostic') {
                            html += '<div class="test-result test-success">✅ قراءة البيانات تعمل</div>';
                        } else {
                            html += '<div class="test-result test-error">❌ قراءة البيانات لا تعمل</div>';
                        }
                    } else {
                        html += '<div class="test-result test-error">❌ كتابة البيانات لا تعمل</div>';
                    }
                    
                    // فحص البيانات الموجودة
                    const products = storage.readFile('products.json');
                    html += `<div class="test-result test-info">📊 عدد المنتجات: ${products ? products.length : 0}</div>`;
                    
                } else {
                    html += '<div class="test-result test-error">❌ كائن التخزين غير موجود</div>';
                }
            } catch (error) {
                html += `<div class="test-result test-error">❌ خطأ في فحص التخزين: ${error.message}</div>`;
            }
            
            results.innerHTML = html;
        }

        function checkModals() {
            const results = document.getElementById('modalResults');
            let html = '<h3>نتائج فحص النوافذ:</h3>';
            
            // فحص وجود عناصر النافذة
            const modal = document.getElementById('testProductModal');
            if (modal) {
                html += '<div class="test-result test-success">✅ عنصر النافذة موجود</div>';
            } else {
                html += '<div class="test-result test-error">❌ عنصر النافذة غير موجود</div>';
            }
            
            // فحص أنماط CSS
            const modalStyles = getComputedStyle(document.documentElement);
            html += '<div class="test-result test-info">🎨 أنماط CSS محملة</div>';
            
            // فحص الوظائف
            if (typeof showAddProductModal === 'function') {
                html += '<div class="test-result test-success">✅ دالة showAddProductModal موجودة</div>';
            } else {
                html += '<div class="test-result test-error">❌ دالة showAddProductModal غير موجودة</div>';
            }
            
            results.innerHTML = html;
        }

        function testProductModal() {
            const modal = document.getElementById('testProductModal');
            if (modal) {
                modal.style.display = 'flex';
            }
        }

        function closeTestProductModal() {
            const modal = document.getElementById('testProductModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        function saveTestProduct(event) {
            event.preventDefault();
            alert('اختبار النافذة نجح! البيانات تم استلامها بشكل صحيح.');
            closeTestProductModal();
        }

        function showErrors() {
            const results = document.getElementById('errorResults');
            
            if (typeof errorHandler !== 'undefined') {
                const errors = errorHandler.getSavedErrors();
                let html = `<h3>سجل الأخطاء (${errors.length} خطأ):</h3>`;
                
                if (errors.length === 0) {
                    html += '<div class="test-result test-success">✅ لا توجد أخطاء مسجلة</div>';
                } else {
                    errors.slice(-5).forEach((error, index) => {
                        html += `
                            <div class="code-block">
                                <strong>${error.type}</strong> - ${error.timestamp}<br>
                                ${JSON.stringify(error.details, null, 2)}
                            </div>
                        `;
                    });
                }
                
                results.innerHTML = html;
            } else {
                results.innerHTML = '<div class="test-result test-error">❌ معالج الأخطاء غير متوفر</div>';
            }
        }

        function clearErrors() {
            if (typeof errorHandler !== 'undefined') {
                errorHandler.clearSavedErrors();
                alert('تم مسح سجل الأخطاء');
                showErrors();
            }
        }

        function resetStorage() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                if (typeof storage !== 'undefined') {
                    storage.clearAllData();
                    storage.initializeDataFiles();
                    alert('تم إعادة تعيين التخزين');
                    location.reload();
                }
            }
        }

        function reloadPage() {
            location.reload();
        }

        // فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkFiles();
                checkStorage();
                checkModals();
            }, 1000);
        });
    </script>
</body>
</html>
