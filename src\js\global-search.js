// نظام البحث الشامل المحسن
class GlobalSearchEngine {
    constructor() {
        this.searchInput = null;
        this.searchResults = null;
        this.currentQuery = '';
        this.searchTimeout = null;
        this.isSearching = false;
    }

    init() {
        this.searchInput = document.getElementById('globalSearchInput');
        this.searchResults = document.getElementById('searchResults');
        
        if (this.searchInput) {
            this.setupEventListeners();
        }
    }

    setupEventListeners() {
        // البحث أثناء الكتابة
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.global-search')) {
                this.hideResults();
            }
        });

        // التنقل بالكيبورد
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });

        // إظهار النتائج عند التركيز
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery.length >= 2) {
                this.showResults();
            }
        });
    }

    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        this.currentQuery = query.trim();

        if (this.currentQuery.length < 2) {
            this.hideResults();
            return;
        }

        // تأخير البحث لتحسين الأداء
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }

    async performSearch(query) {
        this.isSearching = true;
        this.showLoadingState();

        try {
            const results = await this.searchAllData(query);
            this.displayResults(results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showErrorState();
        } finally {
            this.isSearching = false;
        }
    }

    async searchAllData(query) {
        const results = {
            products: [],
            customers: [],
            invoices: []
        };

        // البحث في المنتجات
        try {
            if (typeof storage !== 'undefined') {
                const products = storage.readFile('products.json') || [];
                results.products = products.filter(product => 
                    product.name.toLowerCase().includes(query.toLowerCase()) ||
                    product.category.toLowerCase().includes(query.toLowerCase()) ||
                    (product.description && product.description.toLowerCase().includes(query.toLowerCase()))
                ).slice(0, 5);
            }
        } catch (error) {
            console.log('لا توجد منتجات للبحث فيها');
        }

        // البحث في العملاء
        try {
            if (typeof storage !== 'undefined') {
                const customers = storage.readFile('customers.json') || [];
                results.customers = customers.filter(customer =>
                    customer.name.toLowerCase().includes(query.toLowerCase()) ||
                    customer.phone.includes(query) ||
                    (customer.email && customer.email.toLowerCase().includes(query.toLowerCase()))
                ).slice(0, 5);
            }
        } catch (error) {
            console.log('لا توجد عملاء للبحث فيهم');
        }

        // البحث في الفواتير
        try {
            if (typeof storage !== 'undefined') {
                const invoices = storage.readFile('invoices.json') || [];
                results.invoices = invoices.filter(invoice =>
                    invoice.invoiceNumber.toLowerCase().includes(query.toLowerCase()) ||
                    invoice.customerName.toLowerCase().includes(query.toLowerCase())
                ).slice(0, 5);
            }
        } catch (error) {
            console.log('لا توجد فواتير للبحث فيها');
        }

        return results;
    }

    displayResults(results) {
        if (!this.searchResults) return;

        const totalResults = results.products.length + results.customers.length + results.invoices.length;

        if (totalResults === 0) {
            this.showNoResults();
            return;
        }

        let html = '';

        // عرض المنتجات
        if (results.products.length > 0) {
            html += '<div class="search-category">📦 المنتجات</div>';
            results.products.forEach(product => {
                html += this.createProductItem(product);
            });
        }

        // عرض العملاء
        if (results.customers.length > 0) {
            html += '<div class="search-category">👥 العملاء</div>';
            results.customers.forEach(customer => {
                html += this.createCustomerItem(customer);
            });
        }

        // عرض الفواتير
        if (results.invoices.length > 0) {
            html += '<div class="search-category">🧾 الفواتير</div>';
            results.invoices.forEach(invoice => {
                html += this.createInvoiceItem(invoice);
            });
        }

        this.searchResults.innerHTML = html;
        this.showResults();
    }

    createProductItem(product) {
        const stockStatus = product.stock <= 5 ? 'مخزون منخفض' : `${product.stock} قطعة`;
        return `
            <div class="search-item" onclick="globalSearchEngine.navigateToProduct('${product.id}')">
                <div class="search-item-icon product">
                    <i class="fas fa-box"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${product.name}</div>
                    <div class="search-item-subtitle">${product.category} • ${product.price} جنيه • ${stockStatus}</div>
                </div>
                <div class="search-item-meta">منتج</div>
            </div>
        `;
    }

    createCustomerItem(customer) {
        const createdDate = new Date(customer.createdAt).toLocaleDateString('ar-EG');
        return `
            <div class="search-item" onclick="globalSearchEngine.navigateToCustomer('${customer.id}')">
                <div class="search-item-icon customer">
                    <i class="fas fa-user"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${customer.name}</div>
                    <div class="search-item-subtitle">${customer.phone} • مضاف في ${createdDate}</div>
                </div>
                <div class="search-item-meta">عميل</div>
            </div>
        `;
    }

    createInvoiceItem(invoice) {
        const invoiceDate = new Date(invoice.date).toLocaleDateString('ar-EG');
        const status = invoice.status === 'paid' ? 'مدفوعة' : 'معلقة';
        return `
            <div class="search-item" onclick="globalSearchEngine.navigateToInvoice('${invoice.id}')">
                <div class="search-item-icon invoice">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">فاتورة ${invoice.invoiceNumber}</div>
                    <div class="search-item-subtitle">${invoice.customerName} • ${invoice.total} جنيه • ${status} • ${invoiceDate}</div>
                </div>
                <div class="search-item-meta">فاتورة</div>
            </div>
        `;
    }

    navigateToProduct(productId) {
        this.hideResults();
        this.clearSearch();
        app.loadPage('products');
        setTimeout(() => {
            this.highlightItem('products', productId);
        }, 500);
    }

    navigateToCustomer(customerId) {
        this.hideResults();
        this.clearSearch();
        app.loadPage('customers');
        setTimeout(() => {
            this.highlightItem('customers', customerId);
        }, 500);
    }

    navigateToInvoice(invoiceId) {
        this.hideResults();
        this.clearSearch();
        app.loadPage('invoices');
        setTimeout(() => {
            this.highlightItem('invoices', invoiceId);
        }, 500);
    }

    highlightItem(page, itemId) {
        const row = document.querySelector(`tr[data-id="${itemId}"]`);
        if (row) {
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });
            row.style.backgroundColor = '#fef3c7';
            row.style.transition = 'background-color 0.5s ease';
            
            setTimeout(() => {
                row.style.backgroundColor = '';
            }, 3000);
        }
    }

    showResults() {
        if (this.searchResults) {
            this.searchResults.classList.add('show');
        }
    }

    hideResults() {
        if (this.searchResults) {
            this.searchResults.classList.remove('show');
        }
    }

    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        this.currentQuery = '';
    }

    showLoadingState() {
        if (this.searchResults) {
            this.searchResults.innerHTML = `
                <div class="search-loading" style="padding: 20px; text-align: center; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="margin-bottom: 10px; font-size: 1.2rem;"></i>
                    <div>جاري البحث...</div>
                </div>
            `;
            this.showResults();
        }
    }

    showNoResults() {
        if (this.searchResults) {
            this.searchResults.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <div>لا توجد نتائج للبحث عن "${this.currentQuery}"</div>
                    <div style="font-size: 0.8rem; margin-top: 5px; color: #9ca3af;">جرب البحث بكلمات مختلفة</div>
                </div>
            `;
            this.showResults();
        }
    }

    showErrorState() {
        if (this.searchResults) {
            this.searchResults.innerHTML = `
                <div class="search-error" style="padding: 20px; text-align: center; color: #ef4444;">
                    <i class="fas fa-exclamation-triangle" style="margin-bottom: 10px; font-size: 1.2rem;"></i>
                    <div>حدث خطأ أثناء البحث</div>
                </div>
            `;
            this.showResults();
        }
    }

    handleKeyNavigation(e) {
        if (e.key === 'Escape') {
            this.hideResults();
            this.searchInput.blur();
        }
        if (e.key === 'Enter' && this.currentQuery.length >= 2) {
            e.preventDefault();
            // يمكن إضافة منطق للانتقال للنتيجة الأولى
        }
    }
}

// إنشاء مثيل من محرك البحث الشامل
const globalSearchEngine = new GlobalSearchEngine();

// تهيئة البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تأخير قصير للتأكد من تحميل العناصر
    setTimeout(() => {
        globalSearchEngine.init();
    }, 100);
});
