<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <link rel="stylesheet" href="src/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div style="padding: 20px;">
        <h1>اختبار النظام</h1>
        <button onclick="testStorage()" class="btn btn-primary">اختبار التخزين</button>
        <button onclick="testProductModal()" class="btn btn-secondary">اختبار نافذة المنتج</button>
        <div id="testResults" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;"></div>
        
        <!-- نافذة اختبار -->
        <div id="testModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>اختبار النافذة</h2>
                    <button class="close-btn" onclick="closeTestModal()">&times;</button>
                </div>
                <div style="padding: 20px;">
                    <p>هذه نافذة اختبار للتأكد من عمل النوافذ المنبثقة بشكل صحيح.</p>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeTestModal()">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="src/js/storage.js"></script>
    <script>
        function testStorage() {
            const results = document.getElementById('testResults');
            try {
                // اختبار التخزين
                const testData = { test: 'data', timestamp: new Date().toISOString() };
                const writeResult = storage.writeFile('test.json', testData);
                const readResult = storage.readFile('test.json');
                
                results.innerHTML = `
                    <h3>نتائج اختبار التخزين:</h3>
                    <p><strong>كتابة البيانات:</strong> ${writeResult ? '✅ نجح' : '❌ فشل'}</p>
                    <p><strong>قراءة البيانات:</strong> ${readResult ? '✅ نجح' : '❌ فشل'}</p>
                    <p><strong>البيانات المقروءة:</strong> ${JSON.stringify(readResult, null, 2)}</p>
                `;
            } catch (error) {
                results.innerHTML = `<p style="color: red;">خطأ في اختبار التخزين: ${error.message}</p>`;
            }
        }
        
        function testProductModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'flex';
            }
        }
        
        function closeTestModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('تم تحميل الصفحة');
            console.log('كائن التخزين:', typeof storage);
            
            if (typeof storage !== 'undefined') {
                console.log('✅ تم تحميل نظام التخزين بنجاح');
            } else {
                console.error('❌ فشل في تحميل نظام التخزين');
            }
        });
    </script>
</body>
</html>
